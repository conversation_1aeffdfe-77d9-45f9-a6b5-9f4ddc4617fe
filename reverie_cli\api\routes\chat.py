"""
Enhanced Chat and completion endpoints with AI engine integration.
"""

import time
import uuid
from typing import List, Optional, Dict, Any, AsyncGenerator
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from reverie_cli.core.logging import get_logger
from reverie_cli.core.exceptions import ModelError, ModelNotFoundError
from reverie_cli.api.dependencies import get_model_manager_dep
from reverie_cli.models.manager import get_model_manager


router = APIRouter()
logger = get_logger("chat")


class ChatMessage(BaseModel):
    """Chat message model."""
    role: str = Field(..., description="Message role: 'user', 'assistant', or 'system'")
    content: str = Field(..., description="Message content")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now)


class ChatRequest(BaseModel):
    """Enhanced chat completion request with engine integration."""
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    model: Optional[str] = Field(None, description="Model to use (uses current if not specified)")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(4096, ge=1, le=32768, description="Maximum tokens to generate")
    stream: bool = Field(False, description="Enable streaming response")
    stop: Optional[List[str]] = Field(None, description="Stop sequences")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Top-p sampling")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")

    # Enhanced features
    use_web_search: bool = Field(False, description="Enable web search for real-time information")
    use_memory: bool = Field(True, description="Enable memory for context and learning")
    use_context_analysis: bool = Field(True, description="Enable code context analysis")
    project_path: Optional[str] = Field(None, description="Project path for context analysis")
    context_type: str = Field("general", description="Context type (general, coding, debugging, etc.)")
    remember_conversation: bool = Field(True, description="Remember this conversation for future reference")


class ChatResponse(BaseModel):
    """Chat completion response."""
    id: str = Field(..., description="Unique response ID")
    object: str = Field("chat.completion", description="Object type")
    created: int = Field(..., description="Unix timestamp")
    model: str = Field(..., description="Model used")
    choices: List[Dict[str, Any]] = Field(..., description="Response choices")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")


class CompletionRequest(BaseModel):
    """Text completion request."""
    prompt: str = Field(..., description="Input prompt")
    model: Optional[str] = Field(None, description="Model to use")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(4096, ge=1, le=32768, description="Maximum tokens to generate")
    stream: bool = Field(False, description="Enable streaming response")
    stop: Optional[List[str]] = Field(None, description="Stop sequences")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Top-p sampling")


class CompletionResponse(BaseModel):
    """Text completion response."""
    id: str = Field(..., description="Unique response ID")
    object: str = Field("text_completion", description="Object type")
    created: int = Field(..., description="Unix timestamp")
    model: str = Field(..., description="Model used")
    choices: List[Dict[str, Any]] = Field(..., description="Response choices")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")


@router.post("/chat/completions", response_model=ChatResponse)
async def create_chat_completion(request: ChatRequest):
    """
    Create a chat completion.
    
    Args:
        request: Chat completion request
        
    Returns:
        Chat completion response or streaming response
    """
    try:
        logger.info(f"Chat completion request: {len(request.messages)} messages")
        
        # Validate model is loaded
        model_manager = get_model_manager()
        current_model = request.model or "Lucy-128k"

        # Check if the requested model is loaded
        if not model_manager.is_model_loaded(current_model):
            # If no model is loaded at all, try to load the requested model
            if not model_manager.is_model_loaded():
                try:
                    logger.info(f"Loading requested model: {current_model}")
                    await model_manager.load_model(current_model)
                except Exception as e:
                    logger.error(f"Failed to load model {current_model}: {e}")
                    raise HTTPException(
                        status_code=503,
                        detail=f"Model {current_model} not loaded and failed to load: {e}"
                    )
            else:
                # A different model is loaded, use the currently loaded model
                current_model_info = model_manager.get_current_model_info()
                if current_model_info:
                    current_model = current_model_info.name
                    logger.info(f"Using currently loaded model: {current_model}")
                else:
                    raise HTTPException(status_code=503, detail="No model available")
        
        # Generate response ID
        response_id = f"chatcmpl-{uuid.uuid4().hex[:12]}"
        created = int(time.time())
        
        if request.stream:
            # Return streaming response
            return StreamingResponse(
                generate_chat_stream(request, response_id, created, current_model),
                media_type="text/plain"
            )
        else:
            # Generate non-streaming response
            response_text = await generate_chat_response(request)
            
            # Calculate token usage (mock for now)
            prompt_tokens = sum(len(msg.content.split()) for msg in request.messages)
            completion_tokens = len(response_text.split())
            total_tokens = prompt_tokens + completion_tokens
            
            return ChatResponse(
                id=response_id,
                created=created,
                model=current_model,
                choices=[
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_text
                        },
                        "finish_reason": "stop"
                    }
                ],
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                }
            )
            
    except ModelNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ModelError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Chat completion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat completion failed: {e}")


@router.post("/completions", response_model=CompletionResponse)
async def create_completion(request: CompletionRequest):
    """
    Create a text completion.
    
    Args:
        request: Text completion request
        
    Returns:
        Text completion response or streaming response
    """
    try:
        logger.info(f"Text completion request: {len(request.prompt)} chars")
        
        # Validate model is loaded
        model_manager = get_model_manager()
        current_model = request.model or "Lucy-128k"

        # Check if the requested model is loaded
        if not model_manager.is_model_loaded(current_model):
            # If no model is loaded at all, try to load the requested model
            if not model_manager.is_model_loaded():
                try:
                    logger.info(f"Loading requested model: {current_model}")
                    await model_manager.load_model(current_model)
                except Exception as e:
                    logger.error(f"Failed to load model {current_model}: {e}")
                    raise HTTPException(
                        status_code=503,
                        detail=f"Model {current_model} not loaded and failed to load: {e}"
                    )
            else:
                # A different model is loaded, use the currently loaded model
                current_model_info = model_manager.get_current_model_info()
                if current_model_info:
                    current_model = current_model_info.name
                    logger.info(f"Using currently loaded model: {current_model}")
                else:
                    raise HTTPException(status_code=503, detail="No model available")
        
        # Generate response ID
        response_id = f"cmpl-{uuid.uuid4().hex[:12]}"
        created = int(time.time())
        
        if request.stream:
            # Return streaming response
            return StreamingResponse(
                generate_completion_stream(request, response_id, created, current_model),
                media_type="text/plain"
            )
        else:
            # Generate non-streaming response
            response_text = await generate_completion_response(request)
            
            # Calculate token usage (mock for now)
            prompt_tokens = len(request.prompt.split())
            completion_tokens = len(response_text.split())
            total_tokens = prompt_tokens + completion_tokens
            
            return CompletionResponse(
                id=response_id,
                created=created,
                model=current_model,
                choices=[
                    {
                        "index": 0,
                        "text": response_text,
                        "finish_reason": "stop"
                    }
                ],
                usage={
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens
                }
            )
            
    except ModelNotFoundError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except ModelError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Text completion failed: {e}")
        raise HTTPException(status_code=500, detail=f"Text completion failed: {e}")


async def generate_chat_response(request: ChatRequest) -> str:
    """Generate chat response using the model manager."""
    try:
        model_manager = get_model_manager()

        # Check if any model is loaded
        if not model_manager.is_model_loaded():
            # Try to load a default model if available
            models = model_manager.list_models()
            if models:
                # Try to load the first available model
                default_model = models[0].name
                logger.info(f"No model loaded, attempting to load default model: {default_model}")
                try:
                    await model_manager.load_model(default_model)
                    logger.info(f"Successfully loaded default model: {default_model}")
                except Exception as load_e:
                    logger.error(f"Failed to load default model {default_model}: {load_e}")
                    raise HTTPException(
                        status_code=503,
                        detail=f"No model loaded and failed to load default model: {load_e}"
                    )
            else:
                raise HTTPException(
                    status_code=503,
                    detail="No model loaded and no models available. Please load a model first."
                )

        # Build prompt from messages
        prompt = ""
        for message in request.messages:
            role = message.role
            content = message.content

            if role == "system":
                prompt += f"System: {content}\n"
            elif role == "user":
                prompt += f"User: {content}\n"
            elif role == "assistant":
                prompt += f"Assistant: {content}\n"

        prompt += "Assistant: "

        # Generate response
        response = await model_manager.generate(
            prompt=prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )

        return response

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Chat response generation failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat response generation failed: {e}")


# Enhanced AI-powered endpoints

@router.post("/chat/enhanced", response_model=ChatResponse)
async def enhanced_chat_completion(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    model_manager=Depends(get_model_manager_dep)
):
    """
    Enhanced chat completion with AI engine integration.

    Provides intelligent responses using Web, Context, and Memory engines
    for comprehensive AI assistance similar to Augment.
    """
    try:
        # Initialize enhanced engines
        engines = await _init_enhanced_engines()

        # Get the last user message for analysis
        user_message = None
        for msg in reversed(request.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        if not user_message:
            raise HTTPException(status_code=400, detail="No user message found")

        # Enhanced context building
        enhanced_context = await _build_enhanced_context(
            request, user_message, engines
        )

        # Create enhanced system prompt
        system_prompt = await _create_enhanced_system_prompt(
            request, enhanced_context, engines
        )

        # Prepare enhanced messages
        enhanced_messages = [
            ChatMessage(role="system", content=system_prompt)
        ] + request.messages

        # Get model response
        model = model_manager.get_current_model()
        if not model:
            raise HTTPException(status_code=503, detail="No model loaded")

        # Generate response with enhanced context
        response_content = await _generate_enhanced_response(
            model, enhanced_messages, request
        )

        # Learn from interaction if memory is enabled
        if request.remember_conversation and engines.get("memory"):
            background_tasks.add_task(
                _learn_from_conversation,
                user_message,
                response_content,
                request.context_type,
                engines["memory"]
            )

        # Create response
        response = ChatResponse(
            id=f"chatcmpl-{uuid.uuid4().hex[:8]}",
            created=int(time.time()),
            model=model.name,
            choices=[{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": response_content
                },
                "finish_reason": "stop"
            }],
            usage={
                "prompt_tokens": sum(len(msg.content.split()) for msg in enhanced_messages),
                "completion_tokens": len(response_content.split()),
                "total_tokens": sum(len(msg.content.split()) for msg in enhanced_messages) + len(response_content.split())
            }
        )

        return response

    except ModelError as e:
        logger.error(f"Model error in enhanced chat: {e}")
        raise HTTPException(status_code=503, detail=str(e))
    except Exception as e:
        logger.error(f"Enhanced chat completion error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/chat/smart-code")
async def smart_code_assistance(
    request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    model_manager=Depends(get_model_manager_dep)
):
    """
    Smart code assistance with integrated AI engines.

    Provides intelligent code help including:
    - Code analysis and understanding
    - Web search for solutions
    - Memory-based learning
    - Context-aware suggestions
    """
    try:
        task = request.get("task", "")
        code_context = request.get("code_context", "")
        project_path = request.get("project_path", ".")

        if not task:
            raise HTTPException(status_code=400, detail="Task description required")

        # Initialize engines
        engines = await _init_enhanced_engines()

        # Analyze project context if path provided
        project_analysis = None
        if project_path and engines.get("context"):
            try:
                context_result = await engines["context"].smart_analyze(
                    project_path=project_path,
                    analysis_type="quick",
                    include_ai_insights=True
                )
                if context_result.success:
                    project_analysis = context_result.data
            except Exception as e:
                logger.warning(f"Context analysis failed: {e}")

        # Search for relevant information if needed
        web_info = None
        if engines.get("web") and any(keyword in task.lower() for keyword in ["how to", "best practice", "example", "tutorial"]):
            try:
                search_result = await engines["web"].smart_search(
                    query=f"{task} programming best practices",
                    max_results=3,
                    include_analysis=True
                )
                if search_result.success:
                    web_info = search_result.data
            except Exception as e:
                logger.warning(f"Web search failed: {e}")

        # Recall relevant memories
        memory_context = None
        if engines.get("memory"):
            try:
                memory_result = await engines["memory"].intelligent_search(
                    query=task,
                    search_type="smart",
                    max_results=3
                )
                if memory_result.success:
                    memory_context = memory_result.data
            except Exception as e:
                logger.warning(f"Memory search failed: {e}")

        # Build comprehensive context
        context_parts = [f"Task: {task}"]

        if code_context:
            context_parts.append(f"Code Context:\n{code_context}")

        if project_analysis:
            context_parts.append(f"Project Analysis:\n{project_analysis}")

        if web_info:
            context_parts.append(f"Web Research:\n{web_info}")

        if memory_context:
            context_parts.append(f"Relevant Experience:\n{memory_context}")

        # Create smart code prompt
        smart_prompt = f"""You are an expert AI coding assistant with access to enhanced capabilities.

Context Information:
{chr(10).join(context_parts)}

Please provide comprehensive assistance for the requested task. Include:
1. Complete, working code solutions
2. Explanations of your approach
3. Best practices and optimizations
4. Potential issues and how to avoid them
5. Suggestions for testing and validation

Focus on providing production-ready, well-documented code that follows industry best practices."""

        # Generate response
        model = model_manager.get_current_model()
        if not model:
            raise HTTPException(status_code=503, detail="No model loaded")

        messages = [
            ChatMessage(role="system", content=smart_prompt),
            ChatMessage(role="user", content=task)
        ]

        response_content = await _generate_enhanced_response(model, messages, None)

        # Remember this interaction
        if engines.get("memory"):
            background_tasks.add_task(
                _remember_code_interaction,
                task,
                code_context,
                response_content,
                engines["memory"]
            )

        return {
            "response": response_content,
            "context_used": {
                "project_analysis": project_analysis is not None,
                "web_research": web_info is not None,
                "memory_context": memory_context is not None
            },
            "task": task
        }

    except Exception as e:
        logger.error(f"Smart code assistance error: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Helper functions for enhanced chat

async def _init_enhanced_engines():
    """Initialize enhanced engines for AI capabilities."""
    engines = {}

    try:
        from reverie_cli.tools.web_engine import WebEngine
        engines["web"] = WebEngine()
    except Exception as e:
        logger.warning(f"Failed to initialize web engine: {e}")

    try:
        from reverie_cli.tools.context_engine import ContextEngine
        engines["context"] = ContextEngine()
    except Exception as e:
        logger.warning(f"Failed to initialize context engine: {e}")

    try:
        from reverie_cli.tools.memory_engine import MemoryEngine
        engines["memory"] = MemoryEngine()
    except Exception as e:
        logger.warning(f"Failed to initialize memory engine: {e}")

    return engines


async def _build_enhanced_context(request: ChatRequest, user_message: str, engines: Dict[str, Any]) -> Dict[str, Any]:
    """Build enhanced context using available engines."""
    context = {}

    # Web search if enabled and needed
    if request.use_web_search and engines.get("web"):
        try:
            # Detect if web search would be helpful
            search_indicators = ["how to", "what is", "latest", "current", "best practice", "tutorial", "example"]
            if any(indicator in user_message.lower() for indicator in search_indicators):
                search_result = await engines["web"].smart_search(
                    query=user_message,
                    max_results=3,
                    include_analysis=True
                )
                if search_result.success:
                    context["web_search"] = search_result.data
        except Exception as e:
            logger.warning(f"Web search failed: {e}")

    # Context analysis if enabled and project path provided
    if request.use_context_analysis and request.project_path and engines.get("context"):
        try:
            analysis_result = await engines["context"].smart_analyze(
                project_path=request.project_path,
                analysis_type="quick",
                include_ai_insights=True
            )
            if analysis_result.success:
                context["project_analysis"] = analysis_result.data
        except Exception as e:
            logger.warning(f"Context analysis failed: {e}")

    # Memory search if enabled
    if request.use_memory and engines.get("memory"):
        try:
            memory_result = await engines["memory"].intelligent_search(
                query=user_message,
                search_type="smart",
                max_results=5
            )
            if memory_result.success:
                context["memory_context"] = memory_result.data
        except Exception as e:
            logger.warning(f"Memory search failed: {e}")

    return context


async def _create_enhanced_system_prompt(request: ChatRequest, context: Dict[str, Any], engines: Dict[str, Any]) -> str:
    """Create enhanced system prompt with context and capabilities."""

    # Import prompt manager
    try:
        from reverie_cli.agent.prompts import PromptManager
        prompt_manager = PromptManager()

        # Create dual-mode prompt with available engines
        available_engines = list(engines.keys())
        enhanced_prompt = prompt_manager.create_dual_mode_prompt(
            ai_coder_mode=True,
            api_service_mode=True,
            current_context=request.context_type,
            available_engines=available_engines
        )

        # Add context information
        if context:
            context_info = "\n\n## 📊 Current Context Information\n"

            if context.get("web_search"):
                context_info += "\n### 🌐 Web Search Results:\n"
                results = context["web_search"].get("results", [])
                for i, result in enumerate(results[:3], 1):
                    title = result.get("title", "No title")
                    snippet = result.get("snippet", "No snippet")
                    context_info += f"{i}. **{title}**: {snippet}\n"

            if context.get("project_analysis"):
                analysis = context["project_analysis"]
                context_info += f"\n### 🔍 Project Analysis:\n"
                context_info += f"- Language: {analysis.get('language', 'Unknown')}\n"
                context_info += f"- Framework: {analysis.get('framework', 'None')}\n"
                context_info += f"- Symbols: {analysis.get('symbol_count', 0)}\n"

            if context.get("memory_context"):
                memory_results = context["memory_context"].get("results", [])
                if memory_results:
                    context_info += "\n### 🧠 Relevant Memory:\n"
                    for i, result in enumerate(memory_results[:3], 1):
                        item = result.get("item", {})
                        content = item.get("content", "")[:100]
                        context_info += f"{i}. {content}...\n"

            enhanced_prompt += context_info

        return enhanced_prompt

    except Exception as e:
        logger.warning(f"Failed to create enhanced prompt: {e}")
        # Fallback to basic prompt
        return """You are Reverie, an advanced AI coding assistant with enhanced capabilities.

You have access to web search, code analysis, and memory systems to provide comprehensive assistance.
Always provide helpful, accurate, and contextual responses."""


async def _generate_enhanced_response(model, messages: List[ChatMessage], request: Optional[ChatRequest]) -> str:
    """Generate response using the AI model with enhanced context."""
    try:
        # Convert messages to model format
        model_messages = []
        for msg in messages:
            model_messages.append({
                "role": msg.role,
                "content": msg.content
            })

        # Generate response
        response = await model.generate_response(
            messages=model_messages,
            temperature=request.temperature if request else 0.7,
            max_tokens=request.max_tokens if request else 4096
        )

        return response.get("content", "I apologize, but I couldn't generate a response.")

    except Exception as e:
        logger.error(f"Response generation failed: {e}")
        return "I apologize, but I encountered an error while generating a response."


async def _learn_from_conversation(user_message: str, ai_response: str, context_type: str, memory_engine):
    """Learn from conversation for future reference."""
    try:
        conversation_content = f"User: {user_message}\nAssistant: {ai_response}"

        await memory_engine.smart_remember(
            content=conversation_content,
            memory_type="conversation",
            context=context_type,
            importance=0.6
        )
    except Exception as e:
        logger.warning(f"Failed to learn from conversation: {e}")


async def _remember_code_interaction(task: str, code_context: str, response: str, memory_engine):
    """Remember code interaction for future reference."""
    try:
        interaction_content = f"Task: {task}\nContext: {code_context}\nSolution: {response}"

        await memory_engine.smart_remember(
            content=interaction_content,
            memory_type="code_assistance",
            importance=0.8,
            tags=["coding", "solution", "task"]
        )
    except Exception as e:
        logger.warning(f"Failed to remember code interaction: {e}").strip()

    except Exception as e:
        logger.error(f"Chat generation failed: {e}")
        # Fallback to mock response
        last_message = request.messages[-1].content if request.messages else ""

        if "code" in last_message.lower():
            return """Here's a Python example:

```python
def hello_world():
    print("Hello, World!")
    return "Success"

# Call the function
result = hello_world()
```

This function demonstrates basic Python syntax and returns a success message."""

        elif "help" in last_message.lower():
            return """I'm Reverie CLI, an AI-native code development assistant. I can help you with:

- Writing and reviewing code
- Explaining programming concepts
- Debugging and troubleshooting
- Project architecture and design
- Documentation and testing

What would you like to work on today?"""

        else:
            return f"I understand you said: '{last_message}'. How can I help you with your coding project?"


async def generate_completion_response(request: CompletionRequest) -> str:
    """Generate completion response (mock implementation)."""
    # TODO: Implement actual model inference
    
    prompt = request.prompt.lower()
    
    if prompt.startswith("def "):
        return """
    \"\"\"
    This function performs the requested operation.
    
    Returns:
        The result of the operation
    \"\"\"
    # Implementation here
    pass"""
    
    elif "class " in prompt:
        return """
    \"\"\"
    A class that represents the specified entity.
    \"\"\"
    
    def __init__(self):
        \"\"\"Initialize the class.\"\"\"
        pass
    
    def __str__(self):
        \"\"\"String representation.\"\"\"
        return f"{self.__class__.__name__}()"
"""
    
    else:
        return " This is a completion of your prompt. The AI model would generate relevant content here based on the context and requirements."


async def generate_chat_stream(request: ChatRequest, response_id: str, created: int, model: str) -> AsyncGenerator[str, None]:
    """Generate streaming chat response."""
    # TODO: Implement actual streaming
    
    response_text = await generate_chat_response(request)
    words = response_text.split()
    
    for i, word in enumerate(words):
        chunk = {
            "id": response_id,
            "object": "chat.completion.chunk",
            "created": created,
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "delta": {"content": word + " "},
                    "finish_reason": None
                }
            ]
        }
        
        yield f"data: {chunk}\n\n"
        
        # Simulate streaming delay
        import asyncio
        await asyncio.sleep(0.05)
    
    # Send final chunk
    final_chunk = {
        "id": response_id,
        "object": "chat.completion.chunk", 
        "created": created,
        "model": model,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }
    
    yield f"data: {final_chunk}\n\n"
    yield "data: [DONE]\n\n"


async def generate_completion_stream(request: CompletionRequest, response_id: str, created: int, model: str) -> AsyncGenerator[str, None]:
    """Generate streaming completion response."""
    # TODO: Implement actual streaming
    
    response_text = await generate_completion_response(request)
    words = response_text.split()
    
    for i, word in enumerate(words):
        chunk = {
            "id": response_id,
            "object": "text_completion",
            "created": created,
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "text": word + " ",
                    "finish_reason": None
                }
            ]
        }
        
        yield f"data: {chunk}\n\n"
        
        # Simulate streaming delay
        import asyncio
        await asyncio.sleep(0.05)
    
    # Send final chunk
    final_chunk = {
        "id": response_id,
        "object": "text_completion",
        "created": created,
        "model": model,
        "choices": [
            {
                "index": 0,
                "text": "",
                "finish_reason": "stop"
            }
        ]
    }
    
    yield f"data: {final_chunk}\n\n"
    yield "data: [DONE]\n\n"
