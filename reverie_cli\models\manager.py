"""
Model manager for Reverie CLI.

This module provides the main ModelManager class that handles:
- Model loading and unloading
- Backend selection and management
- Model caching and optimization
- Performance monitoring
"""

import asyncio
from typing import Optional, Dict, List, Any
from datetime import datetime
from pathlib import Path

from reverie_cli.core.logging import get_logger, log_performance_decorator
from reverie_cli.core.config import get_settings
from reverie_cli.core.exceptions import (
    ModelError, ModelNotFoundError, ModelLoadError, ModelInferenceError
)
from reverie_cli.models.info import (
    ModelInfo, ModelStatus, ModelBackend,
    get_model_info, list_available_models, POPULAR_MODELS
)
from reverie_cli.models.backends import BaseModelBackend, create_backend
from reverie_cli.models.detector import get_model_detector


class ModelManager:
    """
    Central model management system.
    
    Handles model loading, unloading, caching, and inference across
    multiple backends with performance optimization.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self.logger = get_logger("model_manager")

        # Current loaded model
        self.current_model: Optional[BaseModelBackend] = None
        self.current_model_info: Optional[ModelInfo] = None

        # Model cache and registry
        self.model_cache: Dict[str, BaseModelBackend] = {}
        self.available_models: Dict[str, ModelInfo] = {}  # Start with empty registry, populate from local scan

        # Performance tracking
        self.load_times: Dict[str, float] = {}
        self.inference_count: Dict[str, int] = {}

        # Model detector for automatic detection
        self.detector = get_model_detector()

        self.logger.info("ModelManager initialized")
    
    async def initialize(self):
        """Initialize the model manager."""
        self.logger.info("Initializing ModelManager")

        # Scan for local models
        await self.scan_local_models()

        # Skip auto-loading default model - let user manually load models
        self.logger.info("Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command")

    async def scan_local_models(self):
        """Scan for local models and update the registry."""
        self.logger.info("Scanning for local models...")

        try:
            # Clear existing registry to avoid showing non-existent models
            self.available_models.clear()

            detected_models = self.detector.scan_for_models()

            # Add detected models to registry
            for detected_model in detected_models:
                model_info = self.detector.create_model_info(detected_model)
                self.available_models[model_info.name] = model_info
                self.logger.debug(f"Added detected model: {model_info.name} ({model_info.backend.value})")

            self.logger.info(f"Found {len(detected_models)} local models")

            # If no local models found, show helpful message
            if len(detected_models) == 0:
                self.logger.info("No local models found. Place model files in ./models/llm/ directory and run 'models scan' to detect them.")

        except Exception as e:
            self.logger.error(f"Failed to scan local models: {e}")
    
    def list_models(self) -> List[ModelInfo]:
        """List all available models."""
        return list(self.available_models.values())
    
    def get_model_info(self, model_name: str) -> Optional[ModelInfo]:
        """Get information about a specific model."""
        return self.available_models.get(model_name)
    
    def get_current_model_info(self) -> Optional[ModelInfo]:
        """Get information about the currently loaded model."""
        return self.current_model_info
    
    def is_model_loaded(self, model_name: Optional[str] = None) -> bool:
        """Check if a model is loaded."""
        if model_name is None:
            return self.current_model is not None
        return (
            self.current_model is not None and 
            self.current_model_info is not None and
            self.current_model_info.name == model_name
        )
    
    @log_performance_decorator("model_load")
    async def load_model(
        self,
        model_name: str,
        backend: Optional[ModelBackend] = None,
        device: Optional[str] = None,
        force_reload: bool = False,
        **kwargs
    ) -> ModelInfo:
        """
        Load a model for inference.
        
        Args:
            model_name: Name or path of the model to load
            backend: Preferred backend (auto-detects if None)
            device: Device to use (auto-detects if None)
            force_reload: Force reload even if already loaded
            **kwargs: Additional backend-specific arguments
            
        Returns:
            ModelInfo object with updated status
            
        Raises:
            ModelNotFoundError: If model is not found
            ModelLoadError: If model loading fails
        """
        self.logger.info(f"Loading model: {model_name}")
        
        # Check if model exists in registry
        if model_name not in self.available_models:
            # Try to find the model by scanning
            await self.scan_local_models()

            if model_name not in self.available_models:
                raise ModelNotFoundError(f"Model not found: {model_name}")

        # Check if already loaded
        if not force_reload and self.is_model_loaded(model_name):
            self.logger.info(f"Model {model_name} already loaded")
            return self.current_model_info

        # Unload current model if different
        if self.current_model and self.current_model_info.name != model_name:
            await self.unload_model()

        # Get model info
        model_info = self.available_models[model_name]
        model_info.status = ModelStatus.LOADING

        try:
            # Auto-detect backend if not specified
            if backend is None:
                backend = await self._auto_detect_backend(model_info)
                self.logger.info(f"Auto-detected backend: {backend.value} for model {model_name}")

            # Determine device
            if device is None:
                device = self._auto_detect_device()

            # Update model info with detected backend
            model_info.backend = backend

            # Create backend
            model_backend = create_backend(model_info, backend)
            
            # Load model with GPU fallback to CPU
            start_time = datetime.now()
            success = False
            final_device = device

            try:
                success = await model_backend.load_model(device=device, **kwargs)
                if success:
                    final_device = device
                else:
                    raise ModelLoadError(f"Backend failed to load model on {device}")

            except Exception as e:
                # If GPU loading failed and we were trying GPU, fallback to CPU
                if device != "cpu" and ("cuda" in device.lower() or "mps" in device.lower()):
                    self.logger.warning(f"Failed to load model on {device}: {e}")
                    self.logger.info("Attempting to fallback to CPU...")

                    try:
                        # Create a new backend instance for CPU
                        model_backend = create_backend(model_info, backend)
                        success = await model_backend.load_model(device="cpu", **kwargs)
                        if success:
                            final_device = "cpu"
                            self.logger.info("Successfully loaded model on CPU as fallback")
                        else:
                            raise ModelLoadError(f"Backend failed to load model on CPU fallback")
                    except Exception as cpu_e:
                        self.logger.error(f"CPU fallback also failed: {cpu_e}")
                        raise ModelLoadError(f"Failed to load model on both {device} and CPU: {e}")
                else:
                    # If we were already trying CPU or it's a different error, re-raise
                    raise ModelLoadError(f"Backend failed to load model on {device}: {e}")

            load_time = (datetime.now() - start_time).total_seconds()

            if not success:
                raise ModelLoadError(f"Backend failed to load model: {model_name}")
            
            # Update state
            model_info.status = ModelStatus.LOADED
            model_info.loaded_at = datetime.now()
            model_info.device = final_device
            
            self.current_model = model_backend
            self.current_model_info = model_info
            self.available_models[model_name] = model_info
            
            # Track performance
            self.load_times[model_name] = load_time
            
            self.logger.info(
                f"Model {model_name} loaded successfully on {final_device} "
                f"using {backend.value} backend in {load_time:.2f}s"
            )
            
            return model_info
            
        except Exception as e:
            model_info.status = ModelStatus.ERROR
            self.available_models[model_name] = model_info
            self.logger.error(f"Failed to load model {model_name}: {e}")
            raise ModelLoadError(f"Failed to load model {model_name}: {e}")
    
    async def unload_model(self) -> bool:
        """
        Unload the currently loaded model.
        
        Returns:
            True if successful, False otherwise
        """
        if not self.current_model:
            self.logger.info("No model currently loaded")
            return True
        
        model_name = self.current_model_info.name
        self.logger.info(f"Unloading model: {model_name}")
        
        try:
            # Unload from backend
            success = await self.current_model.unload_model()
            
            if success:
                # Update model info
                self.current_model_info.status = ModelStatus.AVAILABLE
                self.current_model_info.loaded_at = None
                self.current_model_info.device = None
                
                # Update registry
                self.available_models[model_name] = self.current_model_info
                
                # Clear current model
                self.current_model = None
                self.current_model_info = None
                
                self.logger.info(f"Model {model_name} unloaded successfully")
                return True
            else:
                self.logger.error(f"Backend failed to unload model: {model_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to unload model {model_name}: {e}")
            return False
    
    async def generate(
        self,
        prompt: str,
        max_tokens: int = None,
        temperature: float = None,
        **kwargs
    ) -> str:
        """
        Generate text using the current model.

        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional generation parameters

        Returns:
            Generated text

        Raises:
            ModelInferenceError: If no model loaded or inference fails
        """
        if not self.current_model:
            raise ModelInferenceError("No model currently loaded")

        # Use settings defaults if not provided
        if max_tokens is None:
            max_tokens = self.settings.model.max_tokens
        if temperature is None:
            temperature = self.settings.model.temperature

        # Track inference
        model_name = self.current_model_info.name
        self.inference_count[model_name] = self.inference_count.get(model_name, 0) + 1

        try:
            result = await self.current_model.generate(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )

            self.logger.debug(f"Generated {len(result)} characters for model {model_name}")
            return result

        except Exception as e:
            self.logger.error(f"Generation failed for model {model_name}: {e}")
            raise ModelInferenceError(f"Generation failed: {e}")

    async def generate_stream(
        self,
        prompt: str,
        max_tokens: int = None,
        temperature: float = None,
        **kwargs
    ):
        """
        Generate text using the current model with streaming.

        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens to generate
            temperature: Sampling temperature
            **kwargs: Additional generation parameters

        Yields:
            Generated text chunks

        Raises:
            ModelInferenceError: If no model loaded or inference fails
        """
        if not self.current_model:
            raise ModelInferenceError("No model currently loaded")

        # Use settings defaults if not provided
        if max_tokens is None:
            max_tokens = self.settings.model.max_tokens
        if temperature is None:
            temperature = self.settings.model.temperature

        # Track inference
        model_name = self.current_model_info.name
        self.inference_count[model_name] = self.inference_count.get(model_name, 0) + 1

        try:
            async for chunk in self.current_model.generate_stream(
                prompt=prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            ):
                yield chunk

        except Exception as e:
            self.logger.error(f"Streaming generation failed for model {model_name}: {e}")
            raise ModelInferenceError(f"Streaming generation failed: {e}")

    async def _auto_detect_backend(self, model_info: ModelInfo) -> ModelBackend:
        """Auto-detect the best backend for a model.

        Args:
            model_info: Model information

        Returns:
            Detected backend
        """
        # If backend is already specified and not AUTO, use it
        if model_info.backend != ModelBackend.AUTO:
            return model_info.backend

        # Try to find the actual model files
        models_dir = Path.cwd() / "models" / "llm"

        # Check for GGUF file in gguf subdirectory
        gguf_path = models_dir / "gguf" / f"{model_info.name}.gguf"
        if gguf_path.exists():
            self.logger.info(f"Found GGUF file for {model_info.name}, using GGUF backend")
            return ModelBackend.GGUF

        # Check for GGUF file in root models directory
        root_gguf_path = models_dir / f"{model_info.name}.gguf"
        if root_gguf_path.exists():
            self.logger.info(f"Found GGUF file in root for {model_info.name}, using GGUF backend")
            return ModelBackend.GGUF

        # Check for alternative GGUF naming in gguf directory
        gguf_dir = models_dir / "gguf"
        if gguf_dir.exists():
            for gguf_file in gguf_dir.glob("*.gguf"):
                if model_info.name.lower() in gguf_file.stem.lower():
                    self.logger.info(f"Found GGUF file {gguf_file.name} for {model_info.name}, using GGUF backend")
                    return ModelBackend.GGUF

        # Check for alternative GGUF naming in root directory
        for gguf_file in models_dir.glob("*.gguf"):
            if model_info.name.lower() in gguf_file.stem.lower():
                self.logger.info(f"Found GGUF file {gguf_file.name} in root for {model_info.name}, using GGUF backend")
                return ModelBackend.GGUF

        # Check for Transformers directory in transformers subdirectory
        transformers_path = models_dir / "transformers" / model_info.name
        if transformers_path.exists() and (transformers_path / "config.json").exists():
            self.logger.info(f"Found Transformers directory for {model_info.name}, using Transformers backend")
            return ModelBackend.TRANSFORMERS

        # Check for Transformers directory in root models directory
        root_transformers_path = models_dir / model_info.name
        if root_transformers_path.exists() and root_transformers_path.is_dir():
            config_file = root_transformers_path / "config.json"
            if config_file.exists():
                self.logger.info(f"Found Transformers directory in root for {model_info.name}, using Transformers backend")
                return ModelBackend.TRANSFORMERS

        # Default fallback based on model name patterns
        model_name_lower = model_info.name.lower()
        if any(pattern in model_name_lower for pattern in ["gguf", "q4", "q5", "q8", "f16"]):
            self.logger.info(f"Model name suggests GGUF format, using GGUF backend for {model_info.name}")
            return ModelBackend.GGUF

        # Default to Transformers for HuggingFace-style names
        if "/" in model_info.name:
            self.logger.info(f"Model name suggests HuggingFace format, using Transformers backend for {model_info.name}")
            return ModelBackend.TRANSFORMERS

        # Final fallback
        self.logger.warning(f"Could not auto-detect backend for {model_info.name}, defaulting to Transformers")
        return ModelBackend.TRANSFORMERS

    def _auto_detect_device(self) -> str:
        """Auto-detect the best available device with GPU priority and CPU fallback."""
        device_setting = self.settings.model.device

        if device_setting != "auto":
            return device_setting

        # Try to detect the best available device
        try:
            import torch

            # Check for CUDA (NVIDIA GPU)
            if torch.cuda.is_available():
                device_count = torch.cuda.device_count()
                if device_count > 0:
                    # Use the first available GPU
                    self.logger.info(f"CUDA detected with {device_count} GPU(s), using cuda:0")
                    return "cuda:0"

            # Check for MPS (Apple Silicon)
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                self.logger.info("Apple MPS detected, using mps")
                return "mps"

        except ImportError:
            self.logger.warning("PyTorch not available, falling back to CPU")
        except Exception as e:
            self.logger.warning(f"Error detecting GPU: {e}, falling back to CPU")

        # Fallback to CPU
        self.logger.info("No GPU detected or available, using CPU")
        return "cpu"
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        return {
            "load_times": self.load_times.copy(),
            "inference_count": self.inference_count.copy(),
            "current_model": self.current_model_info.name if self.current_model_info else None,
            "available_models": len(self.available_models),
            "memory_usage": self.current_model.get_memory_usage() if self.current_model else None
        }


# Global model manager instance
_model_manager: Optional[ModelManager] = None


def get_model_manager() -> ModelManager:
    """Get the global model manager instance."""
    global _model_manager
    if _model_manager is None:
        _model_manager = ModelManager()
    return _model_manager
