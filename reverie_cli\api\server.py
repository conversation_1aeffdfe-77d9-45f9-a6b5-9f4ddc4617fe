"""
FastAPI server implementation for Reverie CLI.

This module creates and configures the FastAPI application with all
necessary middleware, routes, and error handlers.
"""

import time
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates

from reverie_cli import __version__, get_logger, get_settings
from reverie_cli.core.exceptions import ReverieError, get_http_status_for_error
from reverie_cli.core.logging import log_startup, log_shutdown
from reverie_cli.api.routes import health, models, chat, enhanced_agent, tools, files, config, web
from reverie_cli.models.manager import get_model_manager
from reverie_cli.tools.manager import get_tool_manager
from reverie_cli.agent.engine import get_agent_engine


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger = get_logger("server")
    log_startup("FastAPI Server", __version__)

    # Initialize components
    try:
        # Initialize model manager
        model_manager = get_model_manager()
        await model_manager.initialize()
        logger.info("Model manager initialized")

        # Initialize tool manager
        tool_manager = get_tool_manager()
        await tool_manager.initialize()
        logger.info("Tool manager initialized")

        # Initialize agent engine
        agent_engine = get_agent_engine()
        await agent_engine.initialize()
        logger.info("Agent engine initialized")

        # Store managers in app state for dependency injection
        app.state.model_manager = model_manager
        app.state.tool_manager = tool_manager
        app.state.agent_engine = agent_engine

        logger.info("All components initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize components: {e}")
        raise

    yield

    # Shutdown
    log_shutdown("FastAPI Server")
    logger.info("Server shutdown complete")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    # Create FastAPI app
    app = FastAPI(
        title="Reverie CLI",
        description="AI-native code development server with Augment-like capabilities",
        version=__version__,
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )
    
    # Add middleware
    setup_middleware(app, settings)
    
    # Add routes
    setup_routes(app)
    
    # Add error handlers
    setup_error_handlers(app)
    
    # Setup static files and templates
    setup_static_files(app)
    
    return app


def setup_middleware(app: FastAPI, settings):
    """Setup middleware for the FastAPI app."""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.server.cors_origins,
        allow_credentials=True,
        allow_methods=settings.server.cors_methods,
        allow_headers=settings.server.cors_headers,
    )
    
    # Gzip compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    
    # Timeout middleware
    @app.middleware("http")
    async def timeout_middleware(request: Request, call_next):
        # Set timeout based on endpoint
        timeout = 300  # Default 5 minutes
        if "/chat/" in request.url.path:
            timeout = 600  # 10 minutes for chat endpoints
        elif "/models/load" in request.url.path:
            timeout = 900  # 15 minutes for model loading

        try:
            response = await asyncio.wait_for(call_next(request), timeout=timeout)
            return response
        except asyncio.TimeoutError:
            logger = get_logger("api")
            logger.error(f"Request timeout after {timeout}s: {request.method} {request.url.path}")
            return JSONResponse(
                status_code=504,
                content={
                    "error": "RequestTimeout",
                    "message": f"Request timed out after {timeout} seconds",
                    "timeout": timeout
                }
            )

    # Request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()

        # Process request
        response = await call_next(request)

        # Log request
        duration = time.time() - start_time
        logger = get_logger("api")
        logger.info(
            f"{request.method} {request.url.path} -> {response.status_code} ({duration:.3f}s)",
            extra={
                "method": request.method,
                "path": request.url.path,
                "status": response.status_code,
                "duration": duration,
                "client": request.client.host if request.client else "unknown",
            }
        )

        return response


def setup_routes(app: FastAPI):
    """Setup API routes."""
    
    # Include route modules
    app.include_router(health.router, prefix="/api/v1", tags=["health"])
    app.include_router(models.router, prefix="/api/v1", tags=["models"])
    app.include_router(chat.router, prefix="/api/v1", tags=["chat"])
    app.include_router(enhanced_agent.router, prefix="/api/v1", tags=["agent"])
    app.include_router(tools.router, prefix="/api/v1", tags=["tools"])
    app.include_router(files.router, prefix="/api/v1", tags=["files"])
    app.include_router(config.router, prefix="/api/v1", tags=["config"])
    app.include_router(web.router, prefix="/api/v1", tags=["web"])
    
    # Root endpoint
    @app.get("/", response_class=HTMLResponse)
    async def root(request: Request):
        """Serve the main web interface."""
        templates = Jinja2Templates(directory="reverie_cli/templates")
        return templates.TemplateResponse(
            "index.html", 
            {"request": request, "version": __version__}
        )
    
    # API info endpoint
    @app.get("/api/info")
    async def api_info():
        """Get API information."""
        settings = get_settings()
        return {
            "name": "Reverie CLI",
            "version": __version__,
            "description": "AI-native code development server",
            "environment": settings.environment,
            "debug": settings.debug,
            "endpoints": {
                "docs": "/docs",
                "redoc": "/redoc",
                "openapi": "/openapi.json",
                "health": "/api/v1/health",
                "models": "/api/v1/models",
                "chat": "/api/v1/chat",
                "agent": "/api/v1/agent",
                "tools": "/api/v1/tools",
                "files": "/api/v1/files",
                "web": "/api/v1/web",
                "web": "/api/v1/web",
            }
        }


def setup_error_handlers(app: FastAPI):
    """Setup error handlers."""
    
    @app.exception_handler(ReverieError)
    async def reverie_error_handler(request: Request, exc: ReverieError):
        """Handle Reverie-specific errors."""
        status_code = get_http_status_for_error(exc)
        return JSONResponse(
            status_code=status_code,
            content=exc.to_dict()
        )
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": "HTTPException",
                "message": exc.detail,
                "status_code": exc.status_code
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger = get_logger("error")
        logger.error(f"Unhandled exception: {exc}", exc_info=True)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "InternalServerError",
                "message": "An internal server error occurred",
                "type": type(exc).__name__
            }
        )


def setup_static_files(app: FastAPI):
    """Setup static files and templates."""
    
    # Mount static files
    app.mount("/static", StaticFiles(directory="reverie_cli/static"), name="static")
    
    # Mount resources
    app.mount("/resources", StaticFiles(directory="resources"), name="resources")
