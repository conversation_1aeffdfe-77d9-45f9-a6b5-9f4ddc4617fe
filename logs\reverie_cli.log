2025-08-01 22:33:17 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:35:00 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:35:27 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:35:52 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:35:53 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:35:53 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:35:53 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:35:53 | ERROR    | reverie_cli.main:run_server:103 - Failed to start server: getaddrinfo() argument 1 must be string or None
2025-08-01 22:36:38 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:36:38 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:36:38 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:36:38 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:36:40 | ERROR    | __main__:run_interactive_console:104 - Console error: 'InteractiveConsole' object has no attribute 'commands'
2025-08-01 22:37:05 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:37:06 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:37:06 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:37:06 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:37:08 | ERROR    | __main__:run_interactive_console:104 - Console error: 'InteractiveConsole' object has no attribute 'commands'
2025-08-01 22:37:24 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:37:24 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:37:24 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:37:24 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:37:26 | ERROR    | __main__:run_interactive_console:104 - Console error: 'InteractiveConsole' object has no attribute 'commands'
2025-08-01 22:41:10 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:41:11 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:41:11 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:41:11 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:41:13 | ERROR    | __main__:run_interactive_console:104 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-01 22:42:09 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:42:09 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:42:09 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:42:09 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:42:51 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:42:51 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-01 22:42:51 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:42:51 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:43:34 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:44:08 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:44:41 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:44:41 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:44:41 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:52:19 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 22:52:19 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 22:52:19 | INFO     | reverie_cli.api.server:lifespan:37 - All components initialized successfully
2025-08-01 22:53:53 | INFO     | reverie_cli.api.server:log_requests:105 - GET /api/v1/health -> 200 (0.006s)
2025-08-01 22:54:02 | INFO     | reverie_cli.api.server:log_requests:105 - GET /api/v1/models -> 200 (0.001s)
2025-08-01 22:54:08 | INFO     | reverie_cli.api.server:log_requests:105 - GET / -> 200 (0.006s)
2025-08-01 22:54:08 | INFO     | reverie_cli.api.server:log_requests:105 - GET /static/js/main.js -> 200 (0.054s)
2025-08-01 22:54:08 | INFO     | reverie_cli.api.server:log_requests:105 - GET /resources/icons/RCS.png -> 200 (0.058s)
2025-08-01 22:54:11 | INFO     | reverie_cli.api.server:log_requests:105 - GET /api/v1/models/current -> 404 (1.008s)
2025-08-01 22:54:11 | INFO     | reverie_cli.api.server:log_requests:105 - GET /api/v1/health/detailed -> 200 (1.008s)
2025-08-01 22:54:30 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:85 - Chat completion request: 1 messages
2025-08-01 22:54:30 | INFO     | reverie_cli.api.server:log_requests:105 - POST /api/v1/chat/completions -> 200 (0.006s)
2025-08-01 22:55:01 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:85 - Chat completion request: 1 messages
2025-08-01 22:55:01 | INFO     | reverie_cli.api.server:log_requests:105 - POST /api/v1/chat/completions -> 200 (0.001s)
2025-08-01 23:00:20 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:00:20 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 23:00:20 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-01 23:00:20 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-01 23:00:20 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-01 23:00:20 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-01 23:00:20 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-01 23:00:21 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-01 23:00:21 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.02s
2025-08-01 23:00:21 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-01 23:00:21 | INFO     | reverie_cli.api.server:lifespan:38 - Model manager initialized
2025-08-01 23:00:21 | INFO     | reverie_cli.api.server:lifespan:42 - All components initialized successfully
2025-08-01 23:01:36 | INFO     | reverie_cli.api.server:log_requests:110 - GET /api/v1/models -> 200 (0.001s)
2025-08-01 23:01:49 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:86 - Chat completion request: 1 messages
2025-08-01 23:01:49 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: User: Write a simple Python function to calculate ...
2025-08-01 23:01:49 | INFO     | reverie_cli.api.server:log_requests:110 - POST /api/v1/chat/completions -> 200 (0.494s)
2025-08-01 23:07:31 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:initialize_default_tools:112 - Initializing default tools
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: read_file
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: write_file
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: list_directory
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: delete_file
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_python
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_shell
2025-08-01 23:07:31 | INFO     | reverie_cli.tools.registry:initialize_default_tools:124 - Initialized 6 default tools
2025-08-01 23:07:31 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 23:07:31 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-01 23:07:31 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-01 23:07:31 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-01 23:07:31 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-01 23:07:31 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-01 23:07:32 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-01 23:07:32 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-01 23:07:32 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-01 23:07:32 | INFO     | reverie_cli.api.server:lifespan:39 - Model manager initialized
2025-08-01 23:07:32 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-01 23:07:32 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-01 23:07:32 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 6 tools
2025-08-01 23:07:32 | INFO     | reverie_cli.api.server:lifespan:44 - Tool manager initialized
2025-08-01 23:07:32 | INFO     | reverie_cli.api.server:lifespan:47 - All components initialized successfully
2025-08-01 23:08:41 | INFO     | reverie_cli.api.server:log_requests:115 - GET /api/v1/tools -> 200 (0.000s)
2025-08-01 23:09:58 | INFO     | reverie_cli.api.server:log_requests:115 - GET /api/v1/tools -> 200 (0.001s)
2025-08-01 23:09:58 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: write_file
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: write_file with parameters: {'path': 'test_output.txt', 'content': 'Hello from Reverie CLI!\nThis is a test file created by the AI assistant.'}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool write_file with parameters: {'path': 'test_output.txt', 'content': 'Hello from Reverie CLI!\nThis is a test file created by the AI assistant.', 'encoding': 'utf-8', 'create_dirs': True}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool write_file executed successfully in 0.000s
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool write_file executed: success=True, time=0.000s
2025-08-01 23:09:58 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/tools/execute -> 200 (0.003s)
2025-08-01 23:09:58 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: read_file
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: read_file with parameters: {'path': 'test_output.txt'}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool read_file with parameters: {'path': 'test_output.txt', 'encoding': 'utf-8'}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool read_file executed successfully in 0.005s
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool read_file executed: success=True, time=0.005s
2025-08-01 23:09:58 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/tools/execute -> 200 (0.008s)
2025-08-01 23:09:58 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: execute_python
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: execute_python with parameters: {'code': '\ndef factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n - 1)\n\n# Test the function\nfor i in range(1, 6):\n    print(f"factorial({i}) = {factorial(i)}")\n'}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool execute_python with parameters: {'code': '\ndef factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n - 1)\n\n# Test the function\nfor i in range(1, 6):\n    print(f"factorial({i}) = {factorial(i)}")\n', 'timeout': 30}
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool execute_python executed successfully in 0.053s
2025-08-01 23:09:58 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool execute_python executed: success=True, time=0.053s
2025-08-01 23:09:58 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/tools/execute -> 200 (0.053s)
2025-08-01 23:11:24 | INFO     | reverie_cli.api.server:log_requests:115 - GET /api/v1/health -> 200 (0.000s)
2025-08-01 23:11:24 | INFO     | reverie_cli.api.server:log_requests:115 - GET /api/v1/models -> 200 (0.001s)
2025-08-01 23:11:24 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:86 - Chat completion request: 1 messages
2025-08-01 23:11:24 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: User: Create a simple Python function that calcula...
2025-08-01 23:11:25 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/chat/completions -> 200 (0.521s)
2025-08-01 23:11:25 | INFO     | reverie_cli.api.server:log_requests:115 - GET /api/v1/tools -> 200 (0.001s)
2025-08-01 23:11:25 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: write_file
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: write_file with parameters: {'path': 'demo_fibonacci.py', 'content': 'def fibonacci(n):\n    """Generate Fibonacci sequence up to n terms."""\n    if n <= 0:\n        return []\n    elif n == 1:\n        return [0]\n    elif n == 2:\n        return [0, 1]\n    \n    fib = [0, 1]\n    for i in range(2, n):\n        fib.append(fib[i-1] + fib[i-2])\n    return fib\n\n# Test the function\nif __name__ == "__main__":\n    n = 10\n    result = fibonacci(n)\n    print(f"Fibonacci sequence ({n} terms): {result}")\n'}
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool write_file with parameters: {'path': 'demo_fibonacci.py', 'content': 'def fibonacci(n):\n    """Generate Fibonacci sequence up to n terms."""\n    if n <= 0:\n        return []\n    elif n == 1:\n        return [0]\n    elif n == 2:\n        return [0, 1]\n    \n    fib = [0, 1]\n    for i in range(2, n):\n        fib.append(fib[i-1] + fib[i-2])\n    return fib\n\n# Test the function\nif __name__ == "__main__":\n    n = 10\n    result = fibonacci(n)\n    print(f"Fibonacci sequence ({n} terms): {result}")\n', 'encoding': 'utf-8', 'create_dirs': True}
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool write_file executed successfully in 0.001s
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool write_file executed: success=True, time=0.001s
2025-08-01 23:11:25 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/tools/execute -> 200 (0.002s)
2025-08-01 23:11:25 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: execute_python
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: execute_python with parameters: {'code': 'def fibonacci(n):\n    """Generate Fibonacci sequence up to n terms."""\n    if n <= 0:\n        return []\n    elif n == 1:\n        return [0]\n    elif n == 2:\n        return [0, 1]\n    \n    fib = [0, 1]\n    for i in range(2, n):\n        fib.append(fib[i-1] + fib[i-2])\n    return fib\n\n# Test the function\nif __name__ == "__main__":\n    n = 10\n    result = fibonacci(n)\n    print(f"Fibonacci sequence ({n} terms): {result}")\n'}
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool execute_python with parameters: {'code': 'def fibonacci(n):\n    """Generate Fibonacci sequence up to n terms."""\n    if n <= 0:\n        return []\n    elif n == 1:\n        return [0]\n    elif n == 2:\n        return [0, 1]\n    \n    fib = [0, 1]\n    for i in range(2, n):\n        fib.append(fib[i-1] + fib[i-2])\n    return fib\n\n# Test the function\nif __name__ == "__main__":\n    n = 10\n    result = fibonacci(n)\n    print(f"Fibonacci sequence ({n} terms): {result}")\n', 'timeout': 30}
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool execute_python executed successfully in 0.049s
2025-08-01 23:11:25 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool execute_python executed: success=True, time=0.049s
2025-08-01 23:11:25 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/tools/execute -> 200 (0.051s)
2025-08-01 23:11:25 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:86 - Chat completion request: 1 messages
2025-08-01 23:11:25 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: User: Create a Python function that implements bub...
2025-08-01 23:11:25 | INFO     | reverie_cli.api.server:log_requests:115 - POST /api/v1/chat/completions -> 200 (0.514s)
2025-08-01 23:11:35 | INFO     | reverie_cli.api.server:log_requests:115 - GET /docs -> 200 (0.000s)
2025-08-01 23:11:35 | INFO     | reverie_cli.api.server:log_requests:115 - GET /openapi.json -> 200 (0.023s)
2025-08-01 23:27:21 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:initialize_default_tools:112 - Initializing default tools
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: read_file
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: write_file
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: list_directory
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: delete_file
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_python
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_shell
2025-08-01 23:27:22 | INFO     | reverie_cli.tools.registry:initialize_default_tools:124 - Initialized 6 default tools
2025-08-01 23:27:22 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-01 23:27:22 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-01 23:27:22 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-01 23:27:22 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-01 23:27:22 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-01 23:27:22 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-01 23:27:23 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-01 23:27:23 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-01 23:27:23 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-01 23:27:23 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-01 23:27:23 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 6 tools
2025-08-01 23:27:23 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.memory:__init__:85 - MemoryManager initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.engine:__init__:48 - AgentEngine initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.engine:initialize:52 - Initializing AgentEngine
2025-08-01 23:27:23 | INFO     | reverie_cli.agent.engine:initialize:69 - AgentEngine initialized with session: b8f9b5c0-a229-44e8-81cd-f18dcefedb8e
2025-08-01 23:27:23 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-01 23:27:23 | INFO     | reverie_cli.api.server:lifespan:52 - All components initialized successfully
2025-08-01 23:28:50 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/health -> 200 (0.000s)
2025-08-01 23:28:51 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/health/detailed -> 200 (1.012s)
2025-08-01 23:28:51 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/health/ready -> 200 (0.001s)
2025-08-01 23:28:51 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/models -> 200 (0.000s)
2025-08-01 23:28:51 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/models/current -> 404 (0.000s)
2025-08-01 23:28:51 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:86 - Chat completion request: 1 messages
2025-08-01 23:28:51 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: User: Hello, can you help me with a simple Python ...
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/chat/completions -> 200 (0.497s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:86 - Chat completion request: 1 messages
2025-08-01 23:28:52 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: User: Write a Python function to calculate factori...
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/chat/completions -> 200 (0.515s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/tools -> 200 (0.000s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: write_file
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: write_file with parameters: {'path': 'test_system_file.txt', 'content': 'This is a test file created by the system test.\nTimestamp: 1754105332.6339848'}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool write_file with parameters: {'path': 'test_system_file.txt', 'content': 'This is a test file created by the system test.\nTimestamp: 1754105332.6339848', 'encoding': 'utf-8', 'create_dirs': True}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool write_file executed successfully in 0.001s
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool write_file executed: success=True, time=0.001s
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/tools/execute -> 200 (0.004s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: read_file
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: read_file with parameters: {'path': 'test_system_file.txt'}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool read_file with parameters: {'path': 'test_system_file.txt', 'encoding': 'utf-8'}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool read_file executed successfully in 0.008s
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool read_file executed: success=True, time=0.008s
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/tools/execute -> 200 (0.010s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.routes.tools:execute_tool:70 - Executing tool: execute_python
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:116 - Executing tool: execute_python with parameters: {'code': '\ndef test_function():\n    result = 2 + 2\n    print(f"Test calculation: 2 + 2 = {result}")\n    return result\n\noutput = test_function()\nprint(f"Function returned: {output}")\n'}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:211 - Executing tool execute_python with parameters: {'code': '\ndef test_function():\n    result = 2 + 2\n    print(f"Test calculation: 2 + 2 = {result}")\n    return result\n\noutput = test_function()\nprint(f"Function returned: {output}")\n', 'timeout': 30}
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.base:safe_execute:217 - Tool execute_python executed successfully in 0.061s
2025-08-01 23:28:52 | INFO     | reverie_cli.tools.manager:execute_tool:131 - Tool execute_python executed: success=True, time=0.061s
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/tools/execute -> 200 (0.063s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/agent/session -> 200 (0.001s)
2025-08-01 23:28:52 | INFO     | reverie_cli.api.routes.agent:execute_agent_task:49 - Agent task: Create a simple Python function that adds two numbers and test it
2025-08-01 23:28:52 | INFO     | reverie_cli.agent.engine:process_request:99 - Processing request: Create a simple Python function that adds two numbers and test it
2025-08-01 23:28:52 | INFO     | reverie_cli.agent.engine:process_request:122 - Planning tasks...
2025-08-01 23:28:52 | INFO     | reverie_cli.agent.planner:plan_tasks:105 - Planning tasks for request: Create a simple Python function that adds two numbers and test it
2025-08-01 23:28:52 | INFO     | reverie_cli.models.backends:generate:132 - Generating response for prompt: 
You are a task planning AI. Break down the follow...
2025-08-01 23:28:53 | INFO     | reverie_cli.agent.planner:plan_tasks:116 - Planned 0 tasks
2025-08-01 23:28:53 | INFO     | reverie_cli.agent.engine:process_request:127 - Executing 0 tasks...
2025-08-01 23:28:53 | INFO     | reverie_cli.agent.executor:execute_tasks:53 - Executing 0 tasks
2025-08-01 23:28:53 | INFO     | reverie_cli.agent.executor:execute_tasks:90 - Completed task execution in 0.0s
2025-08-01 23:28:53 | INFO     | reverie_cli.agent.engine:process_request:162 - Request processed in 0.52s
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - POST /api/v1/agent/execute -> 200 (0.524s)
2025-08-01 23:28:53 | INFO     | reverie_cli.core.config_manager:__init__:34 - ConfigManager initialized
2025-08-01 23:28:53 | WARNING  | reverie_cli.core.config_manager:load_config_file:42 - Config file not found: C:\Users\<USER>\.reverie_cli\config.yaml
2025-08-01 23:28:53 | WARNING  | reverie_cli.core.config_manager:load_config_file:42 - Config file not found: C:\Users\<USER>\.reverie_cli\config.development.yaml
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/config -> 200 (0.002s)
2025-08-01 23:28:53 | WARNING  | reverie_cli.core.config_manager:load_config_file:42 - Config file not found: C:\Users\<USER>\.reverie_cli\config.yaml
2025-08-01 23:28:53 | WARNING  | reverie_cli.core.config_manager:load_config_file:42 - Config file not found: C:\Users\<USER>\.reverie_cli\config.development.yaml
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/config/validate -> 200 (0.001s)
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - GET / -> 200 (0.005s)
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - GET /docs -> 200 (0.000s)
2025-08-01 23:28:53 | INFO     | reverie_cli.api.server:log_requests:120 - GET /openapi.json -> 200 (0.027s)
2025-08-01 23:29:46 | INFO     | reverie_cli.api.server:log_requests:120 - GET /api/v1/health -> 200 (0.001s)
2025-08-01 23:40:02 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:initialize_default_tools:112 - Initializing default tools
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: read_file
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: write_file
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: list_directory
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: delete_file
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_python
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_shell
2025-08-01 23:40:02 | INFO     | reverie_cli.tools.registry:initialize_default_tools:124 - Initialized 6 default tools
2025-08-01 23:41:05 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:initialize_default_tools:112 - Initializing default tools
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: read_file
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: write_file
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: list_directory
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: delete_file
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_python
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_shell
2025-08-01 23:41:05 | INFO     | reverie_cli.tools.registry:initialize_default_tools:124 - Initialized 6 default tools
2025-08-01 23:45:07 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:initialize_default_tools:112 - Initializing default tools
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: read_file
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: write_file
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: list_directory
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: delete_file
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_python
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:register_tool:37 - Registered tool: execute_shell
2025-08-01 23:45:07 | INFO     | reverie_cli.tools.registry:initialize_default_tools:124 - Initialized 6 default tools
2025-08-02 22:21:57 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-04 04:20:27 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-04 04:20:33 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-04 04:20:33 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-04 04:20:35 | INFO     | reverie_cli:<module>:39 - Reverie CLI v0.1.0 initialized
2025-08-04 06:47:06 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:47:06 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:47:06 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:47:06 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:47:06 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:47:06 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:47:06 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:47:07 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:47:07 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:47:07 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:47:07 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:47:07 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:47:07 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:47:07 | ERROR    | reverie_cli.api.server:lifespan:54 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-08-04 06:47:08 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:47:17 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:47:17 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:47:17 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:47:17 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:47:17 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:47:17 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:47:17 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:47:18 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:47:18 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:47:18 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:47:18 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:47:18 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:47:18 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:47:18 | ERROR    | reverie_cli.api.server:lifespan:54 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-08-04 06:47:19 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:48:18 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:48:18 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:48:18 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:48:18 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:48:18 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:48:18 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:48:18 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:48:19 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:48:19 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:48:19 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:48:19 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:48:19 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:48:19 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:48:19 | ERROR    | reverie_cli.api.server:lifespan:54 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-08-04 06:48:20 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:48:39 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:48:39 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:48:39 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:48:39 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:48:39 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:48:39 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:48:39 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:48:40 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:48:40 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.00s
2025-08-04 06:48:40 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:48:40 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:48:40 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:48:40 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:48:40 | ERROR    | reverie_cli.api.server:lifespan:54 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-08-04 06:48:41 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:49:26 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:49:26 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:49:26 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:49:26 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:49:26 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:49:26 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:49:26 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:49:27 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:49:27 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:49:27 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:49:27 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:49:27 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:49:27 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:49:27 | ERROR    | reverie_cli.api.server:lifespan:54 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/list_type
2025-08-04 06:49:28 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:56:33 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:56:33 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:56:33 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:56:33 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:56:33 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:56:33 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:56:33 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:56:34 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:56:34 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:56:34 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:56:34 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:56:34 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:56:34 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:56:34 | ERROR    | reverie_cli.api.server:lifespan:59 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.9/v/list_type
2025-08-04 06:56:35 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 06:57:31 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 06:57:31 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 06:57:31 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 06:57:31 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 06:57:31 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 06:57:31 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 06:57:31 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 06:57:32 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 06:57:32 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 06:57:32 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 06:57:32 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 06:57:32 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 06:57:32 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 06:57:32 | ERROR    | reverie_cli.api.server:lifespan:59 - Failed to initialize components: 1 validation error for ToolInfo
parameters
  Input should be a valid list [type=list_type, input_value={'action': {'type': 'stri...ysis', 'default': True}}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.9/v/list_type
2025-08-04 07:10:11 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 07:10:11 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 07:10:11 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 07:10:11 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 07:10:11 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 07:10:11 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 07:10:11 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 07:10:12 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 07:10:12 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 07:10:12 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 07:10:12 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 07:10:12 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 07:10:12 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 07:10:12 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 07:10:12 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: ad4a46bc-ec3e-417e-a2f1-b0cb3310ad6f
2025-08-04 07:10:12 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 07:10:12 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 07:10:12 | INFO     | reverie_cli.core.logging:log_shutdown:124 - FastAPI Server shutting down
2025-08-04 07:10:12 | INFO     | reverie_cli.api.server:lifespan:66 - Server shutdown complete
2025-08-04 07:19:06 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 07:19:06 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 07:19:06 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 07:19:06 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 07:19:06 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 07:19:06 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 07:19:06 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 07:19:07 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 07:19:07 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 07:19:07 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 07:19:07 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 07:19:07 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 07:19:07 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 07:19:07 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 07:19:07 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 12bcd3d2-1681-441f-8dd6-869a8df7fa3a
2025-08-04 07:19:07 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 07:19:07 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 07:19:07 | INFO     | reverie_cli.core.logging:log_shutdown:124 - FastAPI Server shutting down
2025-08-04 07:19:07 | INFO     | reverie_cli.api.server:lifespan:66 - Server shutdown complete
2025-08-04 07:19:08 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 07:33:12 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 07:33:12 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 07:33:12 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 07:33:12 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 07:33:12 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 07:33:12 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 07:33:12 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 07:33:13 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 07:33:13 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 07:33:13 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 07:33:13 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 07:33:13 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 07:33:13 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 07:33:13 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 07:33:13 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: b58df50f-b201-45ae-90a9-5ac0f2dcd255
2025-08-04 07:33:13 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 07:33:13 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 07:33:13 | INFO     | reverie_cli.core.logging:log_shutdown:124 - FastAPI Server shutting down
2025-08-04 07:33:13 | INFO     | reverie_cli.api.server:lifespan:66 - Server shutdown complete
2025-08-04 07:33:14 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 07:35:53 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 07:35:53 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 07:35:53 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 07:35:53 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 07:35:53 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 07:35:53 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 07:35:53 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 07:35:54 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 07:35:54 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.00s
2025-08-04 07:35:54 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 07:35:54 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 07:35:54 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 07:35:54 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 07:35:54 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 07:35:54 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 89b67730-9935-4358-b768-3b7d9e1b2347
2025-08-04 07:35:54 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 07:35:54 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 07:35:54 | INFO     | reverie_cli.core.logging:log_shutdown:124 - FastAPI Server shutting down
2025-08-04 07:35:54 | INFO     | reverie_cli.api.server:lifespan:66 - Server shutdown complete
2025-08-04 07:35:55 | ERROR    | __main__:run_interactive_console:117 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 07:40:30 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 07:40:30 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 07:40:30 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 07:40:30 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 07:40:30 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 07:40:30 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 07:40:30 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 07:40:31 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 07:40:31 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 07:40:31 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 07:40:31 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 07:40:31 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 07:40:31 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 07:40:31 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 07:40:31 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 7addc0a6-bcc6-491c-b73c-2ffceba4239b
2025-08-04 07:40:31 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 07:40:31 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 08:04:46 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 08:04:46 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 08:04:46 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 08:04:46 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 08:04:46 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 08:04:46 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 08:04:46 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 08:04:47 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 08:04:47 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 08:04:47 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 08:04:47 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 08:04:47 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 08:04:47 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 08:04:47 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 08:04:47 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 53061761-3c87-493d-870b-0c2bca3364e5
2025-08-04 08:04:47 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 08:04:47 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 08:04:48 | ERROR    | __main__:run_interactive_console:152 - Console error: 'str' object has no attribute 'invalidation_hash'
2025-08-04 08:07:43 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 08:07:43 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 08:07:43 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 08:07:43 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 08:07:43 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 08:07:43 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 08:07:43 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 08:07:44 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 08:07:44 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 08:07:44 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 08:07:44 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 08:07:44 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 08:07:44 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 08:07:44 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 08:07:44 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: f69ec665-4c4c-429f-96e0-8356ffa43322
2025-08-04 08:07:44 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 08:07:44 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-04 09:39:43 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-04 09:39:43 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-04 09:39:43 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-04 09:39:43 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-04 09:39:43 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-04 09:39:43 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-04 09:39:43 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-04 09:39:44 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-04 09:39:44 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-04 09:39:44 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-04 09:39:44 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-04 09:39:44 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-04 09:39:44 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-04 09:39:44 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-04 09:39:44 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 05d86992-5f79-4e3b-add7-04a4c3f7e88d
2025-08-04 09:39:44 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-04 09:39:44 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-05 21:54:21 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-05 21:54:21 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-05 21:54:21 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-05 21:54:21 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-05 21:54:21 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-05 21:54:21 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-05 21:54:21 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-05 21:54:22 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-05 21:54:22 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-05 21:54:22 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-05 21:54:22 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-05 21:54:22 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-05 21:54:22 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-05 21:54:22 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-05 21:54:22 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 38b71837-e984-4568-a537-809451bd8d56
2025-08-05 21:54:22 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-05 21:54:22 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-05 21:56:59 | INFO     | reverie_cli.api.server:log_requests:125 - GET /health -> 404 (0.189s)
2025-08-05 21:56:59 | INFO     | reverie_cli.api.server:log_requests:125 - GET /favicon.ico -> 404 (0.001s)
2025-08-05 22:44:21 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v2.0.0 starting up
2025-08-05 22:44:21 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v2.0.0 starting up
2025-08-05 22:44:21 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-05 22:44:21 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-05 22:44:21 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-05 22:44:21 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-05 22:44:21 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-05 22:44:22 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-05 22:44:22 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-05 22:44:22 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-05 22:44:22 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-05 22:44:22 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-05 22:44:22 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-05 22:44:22 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-05 22:44:22 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 2ab8c773-70ab-4e28-850b-eae403612c4b
2025-08-05 22:44:22 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-05 22:44:22 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-05 22:45:43 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-05 22:45:43 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-05 22:45:43 | INFO     | reverie_cli.models.manager:__init__:52 - ModelManager initialized
2025-08-05 22:45:43 | INFO     | reverie_cli.models.manager:initialize:56 - Initializing ModelManager
2025-08-05 22:45:43 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-05 22:45:43 | INFO     | reverie_cli.models.manager:load_model:115 - Loading model: Menlo/Lucy-128k-gguf
2025-08-05 22:45:43 | INFO     | reverie_cli.models.backends:load_model:93 - Loading model Menlo/Lucy-128k-gguf with mock backend
2025-08-05 22:45:44 | INFO     | reverie_cli.models.backends:load_model:105 - Model Menlo/Lucy-128k-gguf loaded successfully
2025-08-05 22:45:44 | INFO     | reverie_cli.models.manager:load_model:166 - Model Menlo/Lucy-128k-gguf loaded successfully on cpu using gguf backend in 1.01s
2025-08-05 22:45:44 | INFO     | reverie_cli.models.manager:initialize:63 - Default model Menlo/Lucy-128k-gguf loaded
2025-08-05 22:45:44 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-05 22:45:44 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-05 22:45:44 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-05 22:45:44 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-05 22:45:44 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: ffa58034-b0c5-4dc5-87d1-cdae846f98e0
2025-08-05 22:45:44 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-05 22:45:44 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-06 21:43:59 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-06 21:43:59 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:scan_local_models:80 - Scanning for local models...
2025-08-06 21:43:59 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 0 models
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:scan_local_models:91 - Found 0 local models
2025-08-06 21:43:59 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:load_model:144 - Loading model: Menlo/Lucy-128k-gguf
2025-08-06 21:43:59 | INFO     | reverie_cli.models.manager:load_model:171 - Auto-detected backend: gguf for model Menlo/Lucy-128k-gguf
2025-08-06 21:43:59 | INFO     | reverie_cli.models.llama_cpp_manager:__init__:68 - LlamaCppManager initialized with directory: H:\RIL\Rilance Code Studio\Reverie Cli\loader\llama.cpp
2025-08-06 21:43:59 | INFO     | reverie_cli.models.backends:_cleanup:362 - GGUF model Menlo/Lucy-128k-gguf unloaded successfully
2025-08-06 21:43:59 | ERROR    | reverie_cli.models.manager:load_model:213 - Failed to load model Menlo/Lucy-128k-gguf: [ModelLoadError] Failed to load GGUF model: [ModelLoadError] GGUF model file not found for Menlo/Lucy-128k-gguf
2025-08-06 21:43:59 | WARNING  | reverie_cli.models.manager:initialize:74 - Failed to load default model Menlo/Lucy-128k-gguf: [ModelLoadError] Failed to load model Menlo/Lucy-128k-gguf: [ModelLoadError] Failed to load GGUF model: [ModelLoadError] GGUF model file not found for Menlo/Lucy-128k-gguf
2025-08-06 21:43:59 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-06 21:43:59 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-06 21:43:59 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-06 21:43:59 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-06 21:43:59 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: d85e3cb8-f7f8-48fa-9562-136ae9c7d6af
2025-08-06 21:43:59 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-06 21:43:59 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-06 21:50:12 | INFO     | reverie_cli.api.server:log_requests:125 - GET / -> 200 (0.007s)
2025-08-06 21:50:12 | INFO     | reverie_cli.api.server:log_requests:125 - GET /static/js/main.js -> 200 (0.046s)
2025-08-06 21:50:12 | INFO     | reverie_cli.api.server:log_requests:125 - GET /resources/icons/RCS.png -> 200 (0.048s)
2025-08-06 21:50:15 | INFO     | reverie_cli.api.server:log_requests:125 - GET /docs -> 200 (0.000s)
2025-08-06 23:28:04 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-06 23:28:04 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-06 23:28:04 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-06 23:28:04 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-06 23:28:04 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-06 23:28:04 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-06 23:28:04 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-06 23:28:04 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-06 23:28:04 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-06 23:28:04 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-06 23:28:04 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-06 23:28:04 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 35353d48-f8d0-440e-abcc-e25122ac2fe6
2025-08-06 23:28:04 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-06 23:28:04 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 03:04:51 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 03:04:51 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 03:04:51 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 03:04:51 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 03:04:51 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 03:04:51 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 03:04:51 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 03:04:51 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 03:04:51 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 03:04:51 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 03:04:51 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 03:04:51 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: e653b456-5f5a-4d75-8ee5-24e92d56e1b1
2025-08-08 03:04:51 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 03:04:51 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 03:05:32 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 03:05:32 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 03:05:32 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 03:05:32 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 03:05:32 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 03:05:32 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 03:05:32 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 03:05:32 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 03:05:32 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 03:05:32 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 03:05:32 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 03:05:32 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 44657648-0cb6-45ad-a54d-2141afdfe974
2025-08-08 03:05:32 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 03:05:32 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 03:06:19 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 03:06:19 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 03:06:19 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 03:06:19 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 03:06:19 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 03:06:19 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 03:06:19 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 03:06:19 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 03:06:19 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 03:06:19 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 03:06:19 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 03:06:19 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 7da1160c-9860-4a79-a98c-2f5268810e7e
2025-08-08 03:06:19 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 03:06:19 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 03:07:07 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 03:07:07 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 03:07:07 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 03:07:07 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 03:07:07 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 03:07:07 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 03:07:07 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 03:07:07 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 03:07:07 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 03:07:07 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 03:07:07 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 03:07:07 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 61c72538-e8e7-4936-90bc-0473820e52e8
2025-08-08 03:07:07 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 03:07:07 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 03:08:23 | INFO     | reverie_cli.api.server:log_requests:125 - GET / -> 200 (0.023s)
2025-08-08 03:08:23 | INFO     | reverie_cli.api.server:log_requests:125 - GET /static/js/main.js -> 200 (0.114s)
2025-08-08 03:08:23 | INFO     | reverie_cli.api.server:log_requests:125 - GET /resources/icons/Reverie Cli.png -> 200 (0.117s)
2025-08-08 03:09:58 | INFO     | reverie_cli.api.server:log_requests:125 - GET /docs -> 200 (0.000s)
2025-08-08 03:10:31 | INFO     | reverie_cli.api.server:log_requests:125 - GET /docs -> 200 (0.000s)
2025-08-08 03:10:32 | INFO     | reverie_cli.api.server:log_requests:125 - GET /openapi.json -> 200 (0.038s)
2025-08-08 08:01:41 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 08:01:41 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 08:01:41 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 08:01:41 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 08:01:41 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 08:01:41 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 08:01:41 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 08:01:41 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 08:01:41 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 08:01:41 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 08:01:41 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 08:01:41 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: db03e7d1-37be-4f81-970f-8126bc63e34b
2025-08-08 08:01:41 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 08:01:41 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 08:03:33 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 08:03:33 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 08:03:33 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 08:03:33 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 08:03:33 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 08:03:33 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 08:03:33 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 08:03:33 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 08:03:33 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 08:03:33 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 08:03:33 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 08:03:33 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 7ed48bb0-de45-4520-abb1-c2c8870ffe51
2025-08-08 08:03:33 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 08:03:33 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 08:10:05 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 08:10:05 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 08:10:05 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 08:10:05 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 08:10:05 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 08:10:05 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 08:10:05 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 08:10:05 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 08:10:05 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 08:10:05 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 08:10:05 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 08:10:05 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 304d0c0b-03e5-4117-9c67-fbdc16c09827
2025-08-08 08:10:05 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 08:10:05 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 09:45:31 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 09:45:32 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 09:45:32 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 09:45:32 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 09:45:32 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 09:45:32 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 09:45:32 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 09:45:32 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 09:45:32 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 09:45:32 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 09:45:32 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 09:45:32 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 352d2fcd-99a7-4bf2-a245-19236df1c570
2025-08-08 09:45:32 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 09:45:32 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 21:36:32 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 21:36:32 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 21:36:32 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 21:36:32 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 21:36:32 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 21:36:32 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 21:36:32 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 21:36:32 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 21:36:32 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 21:36:32 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 21:36:32 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 21:36:32 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 391d6ce0-5383-47ec-9b6a-c5b34671f03f
2025-08-08 21:36:32 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 21:36:32 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 21:49:30 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 21:49:30 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 21:49:30 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 21:49:30 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 21:49:30 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 21:49:30 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 21:49:30 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 21:49:30 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 21:49:30 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 21:49:30 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 21:49:30 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 21:49:30 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 46a35cc4-8457-4cf0-a994-1aa76c5f9c9a
2025-08-08 21:49:30 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 21:49:30 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 23:39:07 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-08 23:39:07 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-08 23:39:07 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-08 23:39:07 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-08 23:39:07 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-08 23:39:07 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-08 23:39:07 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-08 23:39:07 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-08 23:39:07 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-08 23:39:07 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-08 23:39:07 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-08 23:39:07 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 00d0ca4e-2085-4e00-9e29-da8222e8331c
2025-08-08 23:39:07 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-08 23:39:07 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-08 23:39:58 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-08 23:39:58 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-08 23:39:58 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-08 23:39:58 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-08 23:39:58 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-08 23:39:59 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-08 23:39:59 | INFO     | reverie_cli.models.manager:load_model:200 - Model Lucy-128k loaded successfully on cpu using transformers backend in 1.02s
2025-08-08 23:40:31 | INFO     | reverie_cli.models.manager:unload_model:225 - Unloading model: Lucy-128k
2025-08-08 23:40:31 | INFO     | reverie_cli.models.backends:unload_model:113 - Unloading model Lucy-128k
2025-08-08 23:40:31 | INFO     | reverie_cli.models.manager:unload_model:244 - Model Lucy-128k unloaded successfully
2025-08-09 00:15:31 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 00:15:31 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 00:15:31 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 00:15:31 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 00:15:31 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 00:15:31 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 00:15:31 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 00:15:31 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 00:15:31 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 00:15:31 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 00:15:31 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 00:15:31 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: ba4040a8-9604-4121-962a-e3008fb3cbcd
2025-08-09 00:15:31 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 00:15:31 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 00:15:42 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.007s)
2025-08-09 00:39:18 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 00:39:18 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 00:39:18 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 00:39:18 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 00:39:18 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 00:39:18 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 00:39:18 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 00:39:18 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 00:39:18 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 00:39:18 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 00:39:18 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 00:39:18 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 6ca2e098-9dbb-44c5-a50e-c00d14c68e07
2025-08-09 00:39:18 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 00:39:18 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 00:40:03 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.000s)
2025-08-09 00:53:07 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.000s)
2025-08-09 02:23:38 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 02:23:39 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 02:23:39 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 02:23:39 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 02:23:39 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 02:23:39 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 02:23:39 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 02:23:39 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 02:23:39 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 02:23:39 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 02:23:39 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 02:23:39 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 4560f64e-dac8-49cc-802b-e7bb205794c6
2025-08-09 02:23:39 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 02:23:39 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 02:30:50 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 02:30:50 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 02:30:50 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-09 02:30:50 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-09 02:30:50 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-09 02:30:51 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-09 02:30:51 | INFO     | reverie_cli.models.manager:load_model:200 - Model Lucy-128k loaded successfully on cpu using transformers backend in 1.00s
2025-08-09 02:32:02 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.005s)
2025-08-09 02:59:18 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 02:59:18 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 02:59:18 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 02:59:18 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 02:59:18 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 02:59:18 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 02:59:18 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 02:59:18 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 02:59:18 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 02:59:18 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 02:59:18 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 02:59:18 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 0bd877e0-ca59-48d0-bb0a-761d71d37068
2025-08-09 02:59:18 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 02:59:18 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:37:27 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 09:37:27 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 09:37:27 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 09:37:27 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 09:37:27 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 09:37:27 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 09:37:27 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 09:37:27 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 09:37:27 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 09:37:27 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 09:37:27 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 09:37:27 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 68c1ce15-973b-4404-8b63-b55ee1e50da6
2025-08-09 09:37:27 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 09:37:27 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:37:54 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.000s)
2025-08-09 09:38:11 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 09:38:11 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 09:38:11 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-09 09:38:11 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-09 09:38:11 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-09 09:38:12 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-09 09:38:12 | INFO     | reverie_cli.models.manager:load_model:200 - Model Lucy-128k loaded successfully on cpu using transformers backend in 1.01s
2025-08-09 09:38:26 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:94 - Chat completion request: 1 messages
2025-08-09 09:38:26 | ERROR    | reverie_cli.api.routes.chat:generate_chat_response:242 - Chat response generation failed: name 'get_model_manager' is not defined
2025-08-09 09:38:26 | ERROR    | reverie_cli.api.routes.chat:create_chat_completion:145 - Chat completion failed: 500: Chat response generation failed: name 'get_model_manager' is not defined
2025-08-09 09:38:26 | INFO     | reverie_cli.api.server:log_requests:125 - POST /api/v1/chat/completions -> 500 (0.005s)
2025-08-09 09:43:49 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 09:43:50 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 09:43:50 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 09:43:50 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 09:43:50 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 09:43:50 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 09:43:50 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 09:43:50 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 09:43:50 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 09:43:50 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 09:43:50 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 09:43:50 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: ec86765a-a47e-4381-b46a-ba99c46268d6
2025-08-09 09:43:50 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 09:43:50 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:44:02 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 09:44:02 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 09:44:02 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 09:44:02 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 09:44:02 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 09:44:02 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 09:44:02 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 09:44:02 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 09:44:02 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 09:44:02 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 09:44:02 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 09:44:02 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 85019b99-6e54-4f5d-9225-3875c203fb37
2025-08-09 09:44:02 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 09:44:02 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:55:01 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 09:55:01 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 09:55:01 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 09:55:01 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 09:55:01 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 09:55:01 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 09:55:01 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 09:55:01 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 09:55:01 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 09:55:01 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 09:55:01 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 09:55:01 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 5cc086ca-cfed-4b80-8298-250733c67d8a
2025-08-09 09:55:01 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 09:55:01 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:55:57 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 09:55:57 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 09:55:57 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 09:55:57 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 09:55:57 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 09:55:57 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 09:55:57 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 09:55:57 | INFO     | reverie_cli.api.server:lifespan:40 - Model manager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 09:55:57 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 09:55:57 | INFO     | reverie_cli.api.server:lifespan:45 - Tool manager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 09:55:57 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 09:55:57 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 249d0f07-2258-4ca3-a118-573d53798789
2025-08-09 09:55:57 | INFO     | reverie_cli.api.server:lifespan:50 - Agent engine initialized
2025-08-09 09:55:57 | INFO     | reverie_cli.api.server:lifespan:57 - All components initialized successfully
2025-08-09 09:56:38 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 09:56:38 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 09:56:38 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-09 09:56:55 | INFO     | reverie_cli.models.manager:_auto_detect_device:470 - CUDA detected with 1 GPU(s), using cuda:0
2025-08-09 09:56:55 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-09 09:56:55 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-09 09:56:56 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-09 09:56:56 | INFO     | reverie_cli.models.manager:load_model:231 - Model Lucy-128k loaded successfully on cuda:0 using transformers backend in 1.00s
2025-08-09 10:11:12 | INFO     | reverie_cli.api.server:log_requests:125 - GET /api/v1/health -> 200 (0.000s)
2025-08-09 10:18:21 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 10:18:21 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 10:18:21 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 10:18:21 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 10:18:21 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 10:18:21 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 10:18:21 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 10:18:21 | INFO     | reverie_cli.api.server:lifespan:41 - Model manager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 10:18:21 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 10:18:21 | INFO     | reverie_cli.api.server:lifespan:46 - Tool manager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 10:18:21 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 10:18:21 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: c446dbe6-0d17-4792-a3bc-533e87755531
2025-08-09 10:18:21 | INFO     | reverie_cli.api.server:lifespan:51 - Agent engine initialized
2025-08-09 10:18:21 | INFO     | reverie_cli.api.server:lifespan:58 - All components initialized successfully
2025-08-09 10:19:01 | INFO     | reverie_cli.api.server:log_requests:151 - GET /api/v1/health -> 200 (0.001s)
2025-08-09 10:19:06 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:95 - Chat completion request: 1 messages
2025-08-09 10:19:06 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:106 - Loading requested model: Lucy-128k
2025-08-09 10:19:06 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 10:19:06 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 10:19:06 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-09 10:19:10 | INFO     | reverie_cli.models.manager:_auto_detect_device:470 - CUDA detected with 1 GPU(s), using cuda:0
2025-08-09 10:19:10 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-09 10:19:10 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-09 10:19:11 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-09 10:19:11 | INFO     | reverie_cli.models.manager:load_model:231 - Model Lucy-128k loaded successfully on cuda:0 using transformers backend in 0.98s
2025-08-09 10:19:11 | ERROR    | reverie_cli.models.manager:generate:332 - Generation failed for model Lucy-128k: [ModelInferenceError] Model not loaded
2025-08-09 10:19:11 | ERROR    | reverie_cli.api.routes.chat:generate_chat_response:314 - Chat response generation failed: [ModelInferenceError] Generation failed: [ModelInferenceError] Model not loaded
2025-08-09 10:19:11 | ERROR    | reverie_cli.api.routes.chat:create_chat_completion:168 - Chat completion failed: 500: Chat response generation failed: [ModelInferenceError] Generation failed: [ModelInferenceError] Model not loaded
2025-08-09 10:19:11 | INFO     | reverie_cli.api.server:log_requests:151 - POST /api/v1/chat/completions -> 500 (5.400s)
2025-08-09 10:19:45 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 10:19:45 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 10:19:45 | INFO     | reverie_cli.models.manager:load_model:153 - Model Lucy-128k already loaded
2025-08-09 10:19:50 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:95 - Chat completion request: 1 messages
2025-08-09 10:19:50 | ERROR    | reverie_cli.models.manager:generate:332 - Generation failed for model Lucy-128k: [ModelInferenceError] Model not loaded
2025-08-09 10:19:50 | ERROR    | reverie_cli.api.routes.chat:generate_chat_response:314 - Chat response generation failed: [ModelInferenceError] Generation failed: [ModelInferenceError] Model not loaded
2025-08-09 10:19:50 | ERROR    | reverie_cli.api.routes.chat:create_chat_completion:168 - Chat completion failed: 500: Chat response generation failed: [ModelInferenceError] Generation failed: [ModelInferenceError] Model not loaded
2025-08-09 10:19:50 | INFO     | reverie_cli.api.server:log_requests:151 - POST /api/v1/chat/completions -> 500 (0.001s)
2025-08-09 10:29:59 | INFO     | reverie_cli.core.logging:log_startup:118 - Reverie CLI v0.1.0 starting up
2025-08-09 10:30:00 | INFO     | reverie_cli.core.logging:log_startup:118 - FastAPI Server v0.1.0 starting up
2025-08-09 10:30:00 | INFO     | reverie_cli.models.manager:__init__:56 - ModelManager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.models.manager:initialize:60 - Initializing ModelManager
2025-08-09 10:30:00 | INFO     | reverie_cli.models.manager:scan_local_models:70 - Scanning for local models...
2025-08-09 10:30:00 | INFO     | reverie_cli.models.detector:scan_for_models:68 - Detected 2 models
2025-08-09 10:30:00 | INFO     | reverie_cli.models.manager:scan_local_models:84 - Found 2 local models
2025-08-09 10:30:00 | INFO     | reverie_cli.models.manager:initialize:66 - Skipping default model auto-loading - models can be loaded manually using 'models load <model_name>' command
2025-08-09 10:30:00 | INFO     | reverie_cli.api.server:lifespan:41 - Model manager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.tools.manager:__init__:36 - ToolManager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.tools.manager:initialize:40 - Initializing ToolManager
2025-08-09 10:30:00 | INFO     | reverie_cli.tools.manager:initialize:47 - Enabled 8 tools
2025-08-09 10:30:00 | INFO     | reverie_cli.api.server:lifespan:46 - Tool manager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.memory:__init__:98 - Enhanced MemoryManager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.planner:__init__:92 - TaskPlanner initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.executor:__init__:34 - TaskExecutor initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.tools.web_engine:__init__:166 - Enhanced WebEngine initialized with persistent caching and AI capabilities
2025-08-09 10:30:00 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.prompts:__init__:34 - PromptManager initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.engine:__init__:56 - AgentEngine initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.engine:initialize:60 - Initializing AgentEngine
2025-08-09 10:30:00 | INFO     | reverie_cli.agent.engine:initialize:77 - AgentEngine initialized with session: 2566b93d-60f3-4bdf-8a69-b9709d47bb6f
2025-08-09 10:30:00 | INFO     | reverie_cli.api.server:lifespan:51 - Agent engine initialized
2025-08-09 10:30:00 | INFO     | reverie_cli.api.server:lifespan:58 - All components initialized successfully
2025-08-09 10:30:03 | INFO     | reverie_cli.api.server:log_requests:151 - GET /api/v1/health -> 200 (0.001s)
2025-08-09 10:30:13 | INFO     | reverie_cli.core.logging:log_performance:67 - Performance: model_load completed in 0.000s
2025-08-09 10:30:13 | INFO     | reverie_cli.models.manager:load_model:141 - Loading model: Lucy-128k
2025-08-09 10:30:13 | INFO     | reverie_cli.models.manager:load_model:168 - Auto-detected backend: transformers for model Lucy-128k
2025-08-09 10:30:14 | INFO     | reverie_cli.models.manager:_auto_detect_device:470 - CUDA detected with 1 GPU(s), using cuda:0
2025-08-09 10:30:14 | INFO     | reverie_cli.models.backends:load_model:205 - Loading Lucy-128k with Transformers backend
2025-08-09 10:30:14 | INFO     | reverie_cli.models.backends:load_model:96 - Loading model Lucy-128k with mock backend
2025-08-09 10:30:15 | INFO     | reverie_cli.models.backends:load_model:108 - Model Lucy-128k loaded successfully
2025-08-09 10:30:15 | INFO     | reverie_cli.models.manager:load_model:231 - Model Lucy-128k loaded successfully on cuda:0 using transformers backend in 1.01s
2025-08-09 10:30:20 | INFO     | reverie_cli.api.routes.chat:create_chat_completion:95 - Chat completion request: 1 messages
2025-08-09 10:30:20 | INFO     | reverie_cli.models.backends:generate:250 - Generating response for prompt: User: hello
Assistant: ...
2025-08-09 10:30:21 | INFO     | reverie_cli.api.server:log_requests:151 - POST /api/v1/chat/completions -> 200 (0.525s)
2025-08-09 10:31:27 | INFO     | reverie_cli.api.routes.enhanced_agent:execute_agent_task:69 - Executing agent task task-fea80186: auto
2025-08-09 10:31:27 | ERROR    | reverie_cli.api.routes.enhanced_agent:_analyze_task_requirements:343 - Task analysis failed: 'ModelManager' object has no attribute 'get_current_model'
2025-08-09 10:31:27 | INFO     | reverie_cli.tools.context_engine:__init__:225 - Enhanced ContextEngine initialized with AI capabilities and persistent storage
2025-08-09 10:31:27 | ERROR    | reverie_cli.api.routes.enhanced_agent:_execute_general_task:1058 - General task execution failed: 'ModelManager' object has no attribute 'get_current_model'
2025-08-09 10:31:27 | INFO     | reverie_cli.api.server:log_requests:151 - POST /api/v1/agent/execute -> 200 (0.005s)
