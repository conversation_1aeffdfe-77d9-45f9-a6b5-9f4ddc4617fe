@echo off
setlocal enabledelayedexpansion

:: ===============================================================================
::                           Reverie CLI Server Launcher
:: ===============================================================================
:: 
:: This script starts the Reverie CLI server in dedicated server mode.
:: The server provides REST API endpoints and web interface for client connections.
::
:: Author: Rilance Code Studio
:: Version: 1.0
:: ===============================================================================

title Reverie CLI Server

:: Configuration
set "PROJECT_DIR=%~dp0"
set "VENV_NAME=reverie_env"
set "DEFAULT_HOST=127.0.0.1"
set "DEFAULT_PORT=8000"
set "LOG_LEVEL=INFO"

:: Colors for output
set "COLOR_INFO=echo [92m"
set "COLOR_SUCCESS=echo [92m"
set "COLOR_WARNING=echo [93m"
set "COLOR_ERROR=echo [91m"
set "COLOR_RESET=echo [0m"

:: Header
echo.
echo ===============================================================================
echo                           Reverie CLI Server
echo ===============================================================================
echo.
echo [INFO] AI-Native Development Server
echo [INFO] Provides REST API and Web Interface for Reverie CLI clients
echo.

:: Check if virtual environment exists
if not exist "%VENV_NAME%" (
    %COLOR_ERROR%[ERROR] Virtual environment not found: %VENV_NAME%[0m
    echo [INFO] Please run 'setup.bat' first to create the environment
    echo.
    pause
    exit /b 1
)

:: Activate virtual environment
echo [INFO] Activating virtual environment...
call "%VENV_NAME%\Scripts\activate.bat"
if errorlevel 1 (
    %COLOR_ERROR%[ERROR] Failed to activate virtual environment[0m
    pause
    exit /b 1
)

:: Set Python path
set "PYTHONPATH=%PROJECT_DIR%;%PYTHONPATH%"

:: Check dependencies
echo [INFO] Checking dependencies...
python -c "import reverie_cli" 2>nul
if errorlevel 1 (
    %COLOR_WARNING%[WARNING] Reverie CLI module not properly installed[0m
    echo [INFO] Attempting to reinstall...
    python -m pip install -e . >nul 2>&1
    if errorlevel 1 (
        %COLOR_ERROR%[ERROR] Failed to install Reverie CLI[0m
        pause
        exit /b 1
    )
)

:: Get server configuration
set "HOST=%DEFAULT_HOST%"
set "PORT=%DEFAULT_PORT%"

:: Check for custom host/port arguments
if not "%1"=="" (
    if "%1"=="--host" (
        set "HOST=%2"
        shift
        shift
    )
)

if not "%1"=="" (
    if "%1"=="--port" (
        set "PORT=%2"
        shift
        shift
    )
)

:: Display server information
echo.
echo ===============================================================================
echo                            Server Configuration
echo ===============================================================================
echo.
echo [INFO] Server Host: %HOST%
echo [INFO] Server Port: %PORT%
echo [INFO] Log Level: %LOG_LEVEL%
echo [INFO] Project Directory: %PROJECT_DIR%
echo [INFO] Virtual Environment: %VENV_NAME%
echo.

:: Check if port is available
echo [INFO] Checking port availability...
netstat -an | find ":%PORT%" | find "LISTENING" >nul
if not errorlevel 1 (
    %COLOR_WARNING%[WARNING] Port %PORT% is already in use[0m
    echo [INFO] The server will attempt to find an available port
    echo.
)

:: Start server
echo ===============================================================================
echo                              Starting Server
echo ===============================================================================
echo.
echo [INFO] Starting Reverie CLI Server...
echo [INFO] Server Mode: Dedicated API + Web Interface
echo [INFO] Access the web interface at: http://%HOST%:%PORT%
echo [INFO] API documentation at: http://%HOST%:%PORT%/docs
echo.
echo [INFO] Press Ctrl+C to stop the server
echo.

:: Launch the server
python -m reverie_cli.main start --host %HOST% --port %PORT% --no-interactive

:: Handle exit
echo.
if errorlevel 1 (
    %COLOR_ERROR%[ERROR] Server failed to start[0m
    echo [INFO] Check the logs for more information
) else (
    %COLOR_SUCCESS%[SUCCESS] Server stopped gracefully[0m
)

echo.
echo [INFO] Server session ended
pause
exit /b 0
